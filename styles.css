* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
}

header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
}

main {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.filters-section {
    margin-bottom: 30px;
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    align-items: center;
}

.search-container {
    display: flex;
    flex: 1;
    min-width: 300px;
}

#searchInput {
    flex: 1;
    padding: 12px 16px;
    border: 2px solid #e1e5e9;
    border-radius: 8px 0 0 8px;
    font-size: 16px;
    outline: none;
    transition: border-color 0.3s;
}

#searchInput:focus {
    border-color: #667eea;
}

#searchBtn {
    padding: 12px 16px;
    background: #667eea;
    color: white;
    border: none;
    border-radius: 0 8px 8px 0;
    cursor: pointer;
    font-size: 16px;
    transition: background 0.3s;
}

#searchBtn:hover {
    background: #5a6fd8;
}

.filter-container {
    display: flex;
    gap: 15px;
}

select {
    padding: 12px 16px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 16px;
    background: white;
    cursor: pointer;
    outline: none;
    transition: border-color 0.3s;
}

select:focus {
    border-color: #667eea;
}

.stats-section {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 25px;
    border-radius: 12px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

.stat-card h3 {
    font-size: 2.5rem;
    margin-bottom: 8px;
    font-weight: bold;
}

.stat-card p {
    font-size: 1rem;
    opacity: 0.9;
}

.results-section {
    margin-top: 30px;
}

.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #e1e5e9;
}

.results-header h2 {
    color: #333;
    font-size: 1.8rem;
}

#resultsCount {
    color: #666;
    font-size: 1rem;
}

.loading {
    text-align: center;
    padding: 40px;
    font-size: 1.2rem;
    color: #666;
}

.no-data {
    text-align: center;
    padding: 40px;
    color: #666;
}

.no-data h3 {
    margin-bottom: 15px;
    color: #333;
}

.unidades-list {
    display: grid;
    gap: 20px;
}

.unidade-card {
    background: #f8f9fa;
    border: 1px solid #e1e5e9;
    border-radius: 12px;
    padding: 25px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.unidade-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    border-color: #667eea;
}

.unidade-header {
    display: flex;
    justify-content: between;
    align-items: flex-start;
    margin-bottom: 15px;
    flex-wrap: wrap;
    gap: 10px;
}

.unidade-title {
    flex: 1;
}

.unidade-title h3 {
    color: #333;
    font-size: 1.3rem;
    margin-bottom: 8px;
}

.unidade-title .nome-completo {
    color: #555;
    font-size: 1rem;
    font-weight: 500;
    margin-bottom: 4px;
    line-height: 1.4;
}

.unidade-title .nome-oficial {
    color: #666;
    font-size: 0.9rem;
    font-style: italic;
}

.loading-text {
    color: #666;
    font-style: italic;
    font-size: 0.9rem;
}

.error {
    color: #d32f2f;
    font-style: italic;
    font-size: 0.9rem;
}

.load-more-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 10px;
    display: none;
}

.load-more-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.load-more-btn:active {
    transform: translateY(0);
}

.unidade-badges {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    text-transform: uppercase;
}

.badge.extrajudicial {
    background: #e3f2fd;
    color: #1976d2;
}

.badge.judicial {
    background: #f3e5f5;
    color: #7b1fa2;
}

.badge.instalado {
    background: #e8f5e8;
    color: #2e7d32;
}

.badge.extinto {
    background: #ffebee;
    color: #c62828;
}

.unidade-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.detail-item {
    display: flex;
    flex-direction: column;
}

.detail-label {
    font-size: 0.8rem;
    color: #666;
    text-transform: uppercase;
    font-weight: 600;
    margin-bottom: 4px;
}

.detail-value {
    font-size: 0.95rem;
    color: #333;
    word-break: break-word;
}

.detail-value.empty {
    color: #999;
    font-style: italic;
}

@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
    
    header h1 {
        font-size: 2rem;
    }
    
    main {
        padding: 20px;
    }
    
    .filters-section {
        flex-direction: column;
        align-items: stretch;
    }
    
    .search-container {
        min-width: auto;
    }
    
    .filter-container {
        flex-direction: column;
    }
    
    .results-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .unidade-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .unidade-details {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .stats-section {
        grid-template-columns: 1fr;
    }
    
    .stat-card h3 {
        font-size: 2rem;
    }
}