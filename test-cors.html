<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste CORS</title>
</head>
<body>
    <h1>Teste de Requisição CORS</h1>
    <button onclick="testarRequisicao()">Testar Requisição</button>
    <div id="resultado"></div>

    <script>
        async function testarRequisicao() {
            const unidadeId = 12068;
            const url = `https://extrajudicial.tjpr.jus.br/informacoes-das-unidades-extrajudiciais?p_p_id=br_jus_tjpr_portlet_prestacaoContasUnidade_PrestacaoContasUnidadePortlet&p_p_lifecycle=2&p_p_state=normal&p_p_mode=view&p_p_resource_id=%2Fresponsabilidade%2FlistarResponsavelPorUnidade&p_p_cacheability=cacheLevelPage&detalheID=${unidadeId}`;
            
            console.log('Testando URL:', url);
            
            try {
                console.log('Iniciando requisição...');
                
                const response = await fetch(url, {
                    method: 'GET',
                    mode: 'cors',
                    credentials: 'omit',
                    headers: {
                        'Accept': 'application/json, text/plain, */*',
                        'Content-Type': 'application/json'
                    }
                });
                
                console.log('Response status:', response.status);
                console.log('Response headers:', [...response.headers.entries()]);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                console.log('Dados recebidos:', data);
                
                document.getElementById('resultado').innerHTML = `
                    <h3>Sucesso!</h3>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
                
            } catch (error) {
                console.error('Erro na requisição:', error);
                document.getElementById('resultado').innerHTML = `
                    <h3>Erro:</h3>
                    <p style="color: red;">${error.message}</p>
                `;
            }
        }
    </script>
</body>
</html>
