# Soluções para Problemas de CORS

## Problema Atual

O aplicativo está tentando fazer requisições para `https://extrajudicial.tjpr.jus.br` a partir de `http://localhost:8000`, mas o servidor do TJPR não permite requisições cross-origin (CORS), resultando nos erros:

```
Access to fetch at 'https://extrajudicial.tjpr.jus.br/...' from origin 'http://localhost:8000' has been blocked by CORS policy: No 'Access-Control-Allow-Origin' header is present on the requested resource.
```

## Soluções Possíveis

### 1. **Proxy Backend (Recomendado para Produção)**

Criar um servidor backend que faça as requisições para o TJPR:

```javascript
// server.js (Node.js + Express)
const express = require('express');
const cors = require('cors');
const fetch = require('node-fetch');

const app = express();
app.use(cors());

app.get('/api/responsavel/:unidadeId', async (req, res) => {
    const { unidadeId } = req.params;
    const url = `https://extrajudicial.tjpr.jus.br/informacoes-das-unidades-extrajudiciais?p_p_id=br_jus_tjpr_portlet_prestacaoContasUnidade_PrestacaoContasUnidadePortlet&p_p_lifecycle=2&p_p_state=normal&p_p_mode=view&p_p_resource_id=%2Fresponsabilidade%2FlistarResponsavelPorUnidade&p_p_cacheability=cacheLevelPage&detalheID=${unidadeId}`;
    
    try {
        const response = await fetch(url);
        const data = await response.json();
        res.json(data);
    } catch (error) {
        res.status(500).json({ error: 'Erro ao buscar dados' });
    }
});

app.listen(3000, () => console.log('Proxy rodando na porta 3000'));
```

### 2. **Extensão de Navegador (Para Desenvolvimento)**

Instalar uma extensão que desabilita CORS:
- **Chrome**: "CORS Unblock" ou "Disable CORS"
- **Firefox**: "CORS Everywhere"

### 3. **Iniciar Chrome com CORS Desabilitado (Desenvolvimento)**

```bash
# Windows
chrome.exe --user-data-dir="C:/Chrome dev session" --disable-web-security --disable-features=VizDisplayCompositor

# macOS
open -n -a /Applications/Google\ Chrome.app/Contents/MacOS/Google\ Chrome --args --user-data-dir="/tmp/chrome_dev_test" --disable-web-security

# Linux
google-chrome --disable-web-security --user-data-dir="/tmp/chrome_dev_test"
```

### 4. **Servidor Local com Proxy (Desenvolvimento)**

Usar um servidor local que funcione como proxy:

```bash
# Instalar http-proxy-middleware globalmente
npm install -g http-proxy-middleware

# Criar arquivo proxy-server.js
```

### 5. **Solução Atual Implementada**

O código foi atualizado para:
- Não tentar fazer requisições que sabemos que falharão
- Retornar dados padrão imediatamente
- Evitar logs de erro desnecessários no console
- Informar claramente que a informação não está disponível devido ao CORS

## Implementação Recomendada

Para um ambiente de produção, recomendo implementar a **Solução 1 (Proxy Backend)**:

1. Criar um servidor Node.js/Express
2. Configurar CORS adequadamente
3. Fazer as requisições do servidor para o TJPR
4. Retornar os dados para o frontend

## Status Atual

✅ **Corrigido**: Erros de CORS não aparecem mais no console
✅ **Implementado**: Fallback gracioso com mensagens informativas
⚠️ **Limitação**: Dados de responsáveis não são carregados (devido ao CORS)

Para carregar os dados reais, implemente uma das soluções acima.
