class UnidadesManager {
    constructor() {
        this.allUnidades = [];
        this.filteredUnidades = [];
        this.isLoading = false;
        this.responsaveisCache = new Map(); // Cache para os responsáveis
        this.pendingRequests = new Map(); // Para evitar requisições duplicadas
        this.currentLimit = 10; // Limite atual de unidades exibidas
        this.maxLimit = 50; // Limite máximo para evitar sobrecarga
    }

    async init() {
        await this.loadData();
        this.setupEventListeners();
        this.updateStats();
        this.renderUnidades();
    }

    // Função de teste para verificar se a requisição funciona
    async testarRequisicao(unidadeId = 12068) {
        const url = `https://extrajudicial.tjpr.jus.br/informacoes-das-unidades-extrajudiciais?p_p_id=br_jus_tjpr_portlet_prestacaoContasUnidade_PrestacaoContasUnidadePortlet&p_p_lifecycle=2&p_p_state=normal&p_p_mode=view&p_p_resource_id=%2Fresponsabilidade%2FlistarResponsavelPorUnidade&p_p_cacheability=cacheLevelPage&detalheID=${unidadeId}`;

        console.log(`[TEST] Testando requisição para: ${url}`);

        try {
            // Primeiro tentar estabelecer sessão
            try {
                await fetch('https://extrajudicial.tjpr.jus.br/', {
                    method: 'GET',
                    mode: 'cors',
                    credentials: 'include'
                });
                console.log(`[TEST] Sessão estabelecida`);
            } catch (sessionError) {
                console.warn(`[TEST] Não foi possível estabelecer sessão:`, sessionError.message);
            }

            const response = await fetch(url, {
                method: 'GET',
                mode: 'cors',
                credentials: 'include',
                headers: {
                    'Accept': 'application/json, text/plain, */*',
                    'Referer': 'https://extrajudicial.tjpr.jus.br/'
                }
            });

            console.log(`[TEST] Response status: ${response.status}`);
            console.log(`[TEST] Response headers:`, [...response.headers.entries()]);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();
            console.log(`[TEST] Dados recebidos:`, data);
            return data;
        } catch (error) {
            console.error(`[TEST] Erro na requisição:`, error);
            throw error;
        }
    }

    // Buscar informações do responsável pela unidade
    async buscarResponsavel(unidadeId) {
        // Verificar se já está em cache
        if (this.responsaveisCache.has(unidadeId)) {
            return this.responsaveisCache.get(unidadeId);
        }

        // Verificar se já há uma requisição pendente para este ID
        if (this.pendingRequests.has(unidadeId)) {
            return this.pendingRequests.get(unidadeId);
        }

        // Criar a requisição
        const url = `https://extrajudicial.tjpr.jus.br/informacoes-das-unidades-extrajudiciais?p_p_id=br_jus_tjpr_portlet_prestacaoContasUnidade_PrestacaoContasUnidadePortlet&p_p_lifecycle=2&p_p_state=normal&p_p_mode=view&p_p_resource_id=%2Fresponsabilidade%2FlistarResponsavelPorUnidade&p_p_cacheability=cacheLevelPage&detalheID=${unidadeId}`;
        
        const requestPromise = this.fazerRequisicaoComDelay(url, unidadeId);
        this.pendingRequests.set(unidadeId, requestPromise);

        try {
            const resultado = await requestPromise;
            this.responsaveisCache.set(unidadeId, resultado);
            this.pendingRequests.delete(unidadeId);
            return resultado;
        } catch (error) {
            console.error(`[ERROR] Erro ao buscar responsável para unidade ${unidadeId}:`, error.message);
            this.pendingRequests.delete(unidadeId);
            return { nome: 'Não disponível', telefone: 'Não disponível' };
        }
    }

    // Fazer requisição com delay para evitar sobrecarga
    async fazerRequisicaoComDelay(url, unidadeId) {
        // Delay aleatório entre 100ms e 500ms para distribuir as requisições
        const delay = Math.random() * 400 + 100;
        await new Promise(resolve => setTimeout(resolve, delay));

        // Tentar diferentes estratégias para fazer a requisição
        const strategies = [
            // Estratégia 1: Primeiro estabelecer sessão, depois fazer requisição
            async () => {
                console.log(`[DEBUG] Tentando estabelecer sessão e fazer requisição para unidade ${unidadeId}`);

                // Primeiro, acessar a página principal para estabelecer sessão
                try {
                    await fetch('https://extrajudicial.tjpr.jus.br/', {
                        method: 'GET',
                        mode: 'cors',
                        credentials: 'include'
                    });
                    console.log(`[DEBUG] Sessão estabelecida`);
                } catch (sessionError) {
                    console.warn(`[WARN] Não foi possível estabelecer sessão:`, sessionError.message);
                }

                // Agora fazer a requisição da API
                const response = await fetch(url, {
                    method: 'GET',
                    mode: 'cors',
                    credentials: 'include',
                    headers: {
                        'Accept': 'application/json, text/plain, */*',
                        'Referer': 'https://extrajudicial.tjpr.jus.br/'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                console.log(`[DEBUG] Dados recebidos para unidade ${unidadeId}:`, data);

                // Processar os dados retornados baseado na estrutura real da API
                if (data && data.output && data.output.entity && Array.isArray(data.output.entity) && data.output.entity.length > 0) {
                    const responsavelData = data.output.entity[0];

                    // Extrair nome do colaborador
                    let nome = 'Não informado';
                    let telefone = 'Não informado';

                    if (responsavelData.atribuicaoWS && responsavelData.atribuicaoWS.colaboradorWS) {
                        nome = responsavelData.atribuicaoWS.colaboradorWS.nome || 'Não informado';

                        // Tentar extrair telefone
                        if (responsavelData.atribuicaoWS.colaboradorWS.telefone &&
                            Array.isArray(responsavelData.atribuicaoWS.colaboradorWS.telefone) &&
                            responsavelData.atribuicaoWS.colaboradorWS.telefone.length > 0) {
                            telefone = responsavelData.atribuicaoWS.colaboradorWS.telefone[0];
                        }

                        // Tentar extrair email se não houver telefone
                        if (telefone === 'Não informado' && responsavelData.atribuicaoWS.colaboradorWS.email) {
                            telefone = responsavelData.atribuicaoWS.colaboradorWS.email;
                        }
                    }

                    console.log(`[SUCCESS] Responsável encontrado para unidade ${unidadeId}: ${nome}`);
                    return { nome, telefone };
                }

                return { nome: 'Não encontrado', telefone: 'Não informado' };
            },

            // Estratégia 2: Requisição direta sem estabelecer sessão
            async () => {
                console.log(`[DEBUG] Tentando requisição direta para unidade ${unidadeId}`);
                const response = await fetch(url, {
                    method: 'GET',
                    mode: 'cors',
                    credentials: 'include',
                    headers: {
                        'Accept': 'application/json, text/plain, */*'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                console.log(`[DEBUG] Dados recebidos para unidade ${unidadeId}:`, data);

                // Processar os dados (mesmo código da estratégia 1)
                if (data && data.output && data.output.entity && Array.isArray(data.output.entity) && data.output.entity.length > 0) {
                    const responsavelData = data.output.entity[0];

                    let nome = 'Não informado';
                    let telefone = 'Não informado';

                    if (responsavelData.atribuicaoWS && responsavelData.atribuicaoWS.colaboradorWS) {
                        nome = responsavelData.atribuicaoWS.colaboradorWS.nome || 'Não informado';

                        if (responsavelData.atribuicaoWS.colaboradorWS.telefone &&
                            Array.isArray(responsavelData.atribuicaoWS.colaboradorWS.telefone) &&
                            responsavelData.atribuicaoWS.colaboradorWS.telefone.length > 0) {
                            telefone = responsavelData.atribuicaoWS.colaboradorWS.telefone[0];
                        }

                        if (telefone === 'Não informado' && responsavelData.atribuicaoWS.colaboradorWS.email) {
                            telefone = responsavelData.atribuicaoWS.colaboradorWS.email;
                        }
                    }

                    console.log(`[SUCCESS] Responsável encontrado para unidade ${unidadeId}: ${nome}`);
                    return { nome, telefone };
                }

                return { nome: 'Não encontrado', telefone: 'Não informado' };
            },

            // Estratégia 3: Tentar com mode: 'no-cors' (fallback)
            async () => {
                console.log(`[DEBUG] Tentando requisição no-cors para unidade ${unidadeId}`);
                const response = await fetch(url, {
                    method: 'GET',
                    mode: 'no-cors',
                    credentials: 'omit'
                });

                // mode: 'no-cors' não permite ler a resposta, então retornamos dados padrão
                throw new Error('no-cors mode - cannot read response');
            }
        ];

        // Tentar cada estratégia
        for (let i = 0; i < strategies.length; i++) {
            try {
                return await strategies[i]();
            } catch (error) {
                console.warn(`[WARN] Estratégia ${i + 1} falhou para unidade ${unidadeId}:`, error.message);

                // Se é a última estratégia, retornar fallback
                if (i === strategies.length - 1) {
                    console.error(`[ERROR] Todas as estratégias falharam para unidade ${unidadeId}`);
                    return {
                        nome: 'Informação indisponível',
                        telefone: 'Consulte o site oficial'
                    };
                }
                // Continuar para próxima estratégia
                continue;
            }
        }

        // Fallback final (não deveria chegar aqui)
        return { nome: 'Informação indisponível', telefone: 'Consulte o site oficial' };
    }

    async init() {
        await this.loadData();
        this.setupEventListeners();
        this.applyFilters();
    }

    async loadData() {
        try {
            const response = await fetch('informacoes-das-unidades-extrajudiciais.json');
            const data = await response.json();
            
            this.allUnidades = data.output.entity || [];
            this.filteredUnidades = [...this.allUnidades];
            
            document.getElementById('loadingMessage').style.display = 'none';
            
            // Verificar se existem unidades extrajudiciais
            const extrajudiciais = this.allUnidades.filter(u => u.tipoUnidade === 'EXTRAJUDICIAL');
            if (extrajudiciais.length === 0) {
                document.getElementById('noDataMessage').style.display = 'block';
                // Mostrar todas as unidades por padrão se não houver extrajudiciais
                this.applyFilters();
            } else {
                // Se existem extrajudiciais, filtrar apenas elas por padrão
                this.filteredUnidades = extrajudiciais;
                document.getElementById('tipoFilter').value = 'EXTRAJUDICIAL';
            }
            
        } catch (error) {
            console.error('Erro ao carregar dados:', error);
            document.getElementById('loadingMessage').innerHTML = 
                '<p style="color: red;">Erro ao carregar os dados. Verifique se o arquivo JSON está presente.</p>';
        }
    }

    setupEventListeners() {
        const searchInput = document.getElementById('searchInput');
        const searchBtn = document.getElementById('searchBtn');
        const situacaoFilter = document.getElementById('situacaoFilter');
        const tipoFilter = document.getElementById('tipoFilter');

        searchBtn.addEventListener('click', () => this.applyFilters());
        searchInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.applyFilters();
            }
        });
        
        situacaoFilter.addEventListener('change', () => this.applyFilters());
        tipoFilter.addEventListener('change', () => this.applyFilters());
        
        // Busca em tempo real (com debounce)
        let searchTimeout;
        searchInput.addEventListener('input', () => {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => this.applyFilters(), 300);
        });

        // Botão carregar mais
        const loadMoreBtn = document.getElementById('loadMoreBtn');
        if (loadMoreBtn) {
            loadMoreBtn.addEventListener('click', () => this.loadMoreUnidades());
        }
    }

    applyFilters() {
        const searchTerm = document.getElementById('searchInput')?.value.toLowerCase() || '';
        const situacaoFilter = document.getElementById('situacaoFilter')?.value || '';
        const tipoFilter = document.getElementById('tipoFilter')?.value || '';

        // Resetar limite quando aplicar novos filtros
        this.currentLimit = 10;

        const allFiltered = this.allUnidades.filter(unidade => {
            const matchesSearch = !searchTerm || 
                unidade.nomeUsual?.toLowerCase().includes(searchTerm) ||
                unidade.nomeCompleto?.toLowerCase().includes(searchTerm) ||
                unidade.nomeOficial?.toLowerCase().includes(searchTerm) ||
                unidade.sigla?.toLowerCase().includes(searchTerm) ||
                unidade.id?.toString().includes(searchTerm);

            // Filtrar apenas unidades INSTALADO (não mostrar EXTINTO)
            const matchesSituacao = unidade.situacao === 'INSTALADO';
            const matchesTipo = !tipoFilter || unidade.tipoUnidade === tipoFilter;

            return matchesSearch && matchesSituacao && matchesTipo;
        });

        // Aplicar limite atual
        this.filteredUnidades = allFiltered.slice(0, this.currentLimit);
        this.allFilteredUnidades = allFiltered; // Guardar todos os resultados filtrados

        this.updateLoadMoreButton();
        this.updateStats();
        this.renderUnidades();
    }

    loadMoreUnidades() {
        if (this.currentLimit < this.maxLimit && this.allFilteredUnidades) {
            this.currentLimit = Math.min(this.currentLimit + 10, this.maxLimit);
            this.filteredUnidades = this.allFilteredUnidades.slice(0, this.currentLimit);
            this.updateLoadMoreButton();
            this.renderUnidades();
        }
    }

    updateLoadMoreButton() {
        const loadMoreBtn = document.getElementById('loadMoreBtn');
        if (loadMoreBtn) {
            const hasMore = this.allFilteredUnidades && 
                           this.allFilteredUnidades.length > this.currentLimit && 
                           this.currentLimit < this.maxLimit;
            
            loadMoreBtn.style.display = hasMore ? 'block' : 'none';
            
            if (hasMore) {
                const remaining = Math.min(
                    this.allFilteredUnidades.length - this.currentLimit,
                    10
                );
                loadMoreBtn.textContent = `Carregar mais ${remaining} unidades`;
            }
        }
    }

    updateStats() {
        const total = this.allUnidades.length;
        const extrajudiciais = this.allUnidades.filter(u => u.tipoUnidade === 'EXTRAJUDICIAL').length;
        const instaladas = this.allUnidades.filter(u => u.situacao === 'INSTALADO').length;

        document.getElementById('totalUnidades').textContent = total.toLocaleString();
        document.getElementById('unidadesExtrajudiciais').textContent = extrajudiciais.toLocaleString();
        document.getElementById('unidadesInstaladas').textContent = instaladas.toLocaleString();
        
        document.getElementById('resultsCount').textContent = 
            `${this.filteredUnidades.length.toLocaleString()} unidades encontradas`;
    }

    renderUnidades() {
        const container = document.getElementById('unidadesList');
        
        if (this.filteredUnidades.length === 0) {
            if (this.allUnidades.length === 0) {
                container.innerHTML = '<div class="no-data">Nenhuma unidade encontrada.</div>';
            } else {
                const extrajudiciaisCount = this.allUnidades.filter(u => u.tipoUnidade === 'EXTRAJUDICIAL').length;
                if (extrajudiciaisCount === 0) {
                    container.innerHTML = `
                        <div class="no-data">
                            <h3>Nenhuma unidade extrajudicial encontrada</h3>
                            <p>O arquivo contém ${this.allUnidades.length} unidades, mas todas são do tipo JUDICIAL.</p>
                            <p>Para visualizar todas as unidades, altere o filtro de tipo.</p>
                        </div>
                    `;
                } else {
                    container.innerHTML = '<div class="no-data">Nenhuma unidade corresponde aos filtros aplicados.</div>';
                }
            }
            return;
        }

        container.innerHTML = this.filteredUnidades.map(unidade => this.createUnidadeCard(unidade)).join('');
        
        // Carregar informações dos responsáveis após renderizar
        this.filteredUnidades.forEach(unidade => {
            this.carregarResponsavelUnidade(unidade.id);
        });
    }

    async carregarResponsavelUnidade(unidadeId) {
        try {
            const responsavel = await this.buscarResponsavel(unidadeId);
            
            // Atualizar o nome do responsável
            const responsavelElement = document.getElementById(`responsavel-${unidadeId}`);
            if (responsavelElement) {
                responsavelElement.innerHTML = `<strong>Titular:</strong> ${responsavel.nome}`;
                responsavelElement.classList.remove('loading-text');
            }
            
            // Atualizar o telefone
            const telefoneElement = document.getElementById(`telefone-${unidadeId}`);
            if (telefoneElement) {
                telefoneElement.textContent = responsavel.telefone;
                telefoneElement.classList.remove('loading-text');
            }
        } catch (error) {
            // Tratamento silencioso de erro - não mostrar no console
            
            // Mostrar mensagem amigável nos elementos
            const responsavelElement = document.getElementById(`responsavel-${unidadeId}`);
            if (responsavelElement) {
                responsavelElement.innerHTML = '<strong>Titular:</strong> <span class="error">Informação indisponível</span>';
                responsavelElement.classList.remove('loading-text');
            }
            
            const telefoneElement = document.getElementById(`telefone-${unidadeId}`);
            if (telefoneElement) {
                telefoneElement.innerHTML = '<span class="error">Consulte o site oficial</span>';
                telefoneElement.classList.remove('loading-text');
            }
        }
    }

    createUnidadeCard(unidade) {
        const tipoClass = unidade.tipoUnidade?.toLowerCase() || 'unknown';
        const situacaoClass = unidade.situacao?.toLowerCase() || 'unknown';
        
        return `
            <div class="unidade-card" onclick="this.classList.toggle('expanded')">
                <div class="unidade-header">
                    <div class="unidade-title">
                        <h3>${this.formatValue(unidade.nomeUsual || unidade.nomeOficial)}</h3>
                        <p class="nome-completo" id="responsavel-${unidade.id}">
                            <span class="loading-text">Carregando titular...</span>
                        </p>
                        <p class="nome-oficial">${this.formatValue(unidade.nomeOficial)}</p>
                    </div>
                    <div class="unidade-badges">
                        <span class="badge ${tipoClass}">${unidade.tipoUnidade || 'N/A'}</span>
                        <span class="badge ${situacaoClass}">${unidade.situacao || 'N/A'}</span>
                    </div>
                </div>
                
                <div class="unidade-details">
                    <div class="detail-item">
                        <span class="detail-label">Nome Completo</span>
                        <span class="detail-value">${this.formatValue(unidade.nomeCompleto)}</span>
                    </div>
                    
                    <div class="detail-item">
                        <span class="detail-label">Telefone</span>
                        <span class="detail-value" id="telefone-${unidade.id}">Carregando...</span>
                    </div>
                    
                    <div class="detail-item">
                        <span class="detail-label">ID</span>
                        <span class="detail-value">${this.formatValue(unidade.id)}</span>
                    </div>
                    
                    <div class="detail-item">
                        <span class="detail-label">Sigla</span>
                        <span class="detail-value">${this.formatValue(unidade.sigla)}</span>
                    </div>
                    
                    <div class="detail-item">
                        <span class="detail-label">Email</span>
                        <span class="detail-value">${this.formatValue(unidade.emailUnidade)}</span>
                    </div>
                    
                    <div class="detail-item">
                        <span class="detail-label">CNPJ</span>
                        <span class="detail-value">${this.formatValue(unidade.cnpj)}</span>
                    </div>
                    
                    <div class="detail-item">
                        <span class="detail-label">Código DOMUS</span>
                        <span class="detail-value">${this.formatValue(unidade.codDomus)}</span>
                    </div>
                    
                    <div class="detail-item">
                        <span class="detail-label">Pode Lotar</span>
                        <span class="detail-value">${unidade.podeLotar ? 'Sim' : 'Não'}</span>
                    </div>
                    
                    ${unidade.tipoUnidadeExtraJudicial ? `
                    <div class="detail-item">
                        <span class="detail-label">Tipo Extrajudicial</span>
                        <span class="detail-value">${this.formatValue(unidade.tipoUnidadeExtraJudicial)}</span>
                    </div>
                    ` : ''}
                    
                    ${unidade.informacaoComplementar ? `
                    <div class="detail-item">
                        <span class="detail-label">Informação Complementar</span>
                        <span class="detail-value">${this.formatValue(unidade.informacaoComplementar)}</span>
                    </div>
                    ` : ''}
                    
                    ${unidade.paginaInternetUnidade ? `
                    <div class="detail-item">
                        <span class="detail-label">Página Internet</span>
                        <span class="detail-value">
                            <a href="${unidade.paginaInternetUnidade}" target="_blank">${unidade.paginaInternetUnidade}</a>
                        </span>
                    </div>
                    ` : ''}
                </div>
            </div>
        `;
    }

    formatValue(value) {
        if (value === null || value === undefined || value === '') {
            return '<span class="empty">Não informado</span>';
        }
        if (value === 'Não Informado.' || value === 'Não Informado') {
            return '<span class="empty">Não informado</span>';
        }
        return value;
    }
}

// Inicializar quando a página carregar
document.addEventListener('DOMContentLoaded', () => {
    const manager = new UnidadesManager();
    manager.init();

    // Expor o manager globalmente para testes no console
    window.unidadesManager = manager;

    // Função global para testar requisições
    window.testarRequisicao = (unidadeId) => manager.testarRequisicao(unidadeId);

    console.log('[INFO] Para testar requisições, use: testarRequisicao(12068) no console');
});

// Adicionar funcionalidade de expansão de cards
document.addEventListener('click', (e) => {
    if (e.target.closest('.unidade-card')) {
        const card = e.target.closest('.unidade-card');
        card.classList.toggle('expanded');
    }
});

// Adicionar estilos para cards expandidos
const style = document.createElement('style');
style.textContent = `
    .unidade-card.expanded {
        background: #f0f4ff;
        border-color: #667eea;
    }
    
    .unidade-card.expanded .unidade-details {
        margin-top: 20px;
        padding-top: 20px;
        border-top: 1px solid #e1e5e9;
    }
    
    .loading-text {
        color: #666;
        font-style: italic;
    }
    
    .error {
        color: #d32f2f;
        font-style: italic;
    }
`;
document.head.appendChild(style);